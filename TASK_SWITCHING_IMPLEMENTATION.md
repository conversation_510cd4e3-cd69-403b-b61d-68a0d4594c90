# 真正的任务切换实现 (Real Task Switching Implementation)

## 🎯 问题分析

您指出的问题非常准确：**之前的实现并没有真正实现客户端在任务间的服务器切换**。

### 之前的错误实现
```cpp
// 错误：预先为每个任务创建独立的客户端应用
CreateClientApplications() {
    for each task:
        create BulkSendHelper(serverAddress, serverPort)  // 预先绑定服务器
        clientApps[clientId, taskId] = app
}

// 错误：只是在不同时间启动预先创建的应用
ScheduleTaskExecution() {
    for each task:
        clientApp.Start(startTime)  // 启动预先绑定的应用
        clientApp.Stop(stopTime)
}
```

**问题**：每个客户端应用在创建时就已经绑定到特定服务器，没有真正的切换。

## ✅ 新的正确实现

### 真正的任务切换机制
```cpp
// 正确：动态创建和切换连接
ScheduleClientTaskSwitching() {
    for each task in priority order:
        // 在任务开始时动态创建连接
        Simulator::Schedule(transmissionStartTime, 
                          &StartSingleTask, 
                          clientId, taskExecution, interfaces, stopTime)
}

StartSingleTask() {
    // 动态获取当前任务的服务器地址
    serverAddress = interfaces.GetAddress(numClients + taskExecution.taskId)
    serverPort = 9000 + taskExecution.taskId
    
    // 创建新的连接到正确的服务器
    BulkSendHelper bulkSendHelper("ns3::TcpSocketFactory",
                                  InetSocketAddress(serverAddress, serverPort))
    
    // 立即启动应用
    taskApp = bulkSendHelper.Install(clientNode)
    taskApp.Start(Seconds(0.0))
}
```

## 🔄 任务切换流程

### 1. 服务器设置
```
Task 0 Server: Node (numClients + 0), Port 9000, IP 10.1.1.X
Task 1 Server: Node (numClients + 1), Port 9001, IP 10.1.1.Y  
Task 2 Server: Node (numClients + 2), Port 9002, IP 10.1.1.Z
```

### 2. 客户端执行时间线
```
Client 0 (按优先级执行):
  0.0s - 3.0s: 计算 Task 2 (priority=0.9)
  3.0s - 5.0s: 连接 Server 2 (10.1.1.Z:9002) 传输 Task 2
  5.0s - 7.0s: 计算 Task 0 (priority=0.5)  
  7.0s - 9.0s: 连接 Server 0 (10.1.1.X:9000) 传输 Task 0
  9.0s - 10.5s: 计算 Task 1 (priority=0.3)
  10.5s - 12.5s: 连接 Server 1 (10.1.1.Y:9001) 传输 Task 1
```

### 3. 关键时间点的动作
```cpp
// t=0.0s: 配置 Task 2 网络参数
Simulator::Schedule(Seconds(0.0), &ConfigureNetworkForTask, task2)

// t=3.0s: 开始 Task 2 传输，连接到 Server 2
Simulator::Schedule(Seconds(3.0), &StartSingleTask, client0, task2, server2)

// t=5.0s: 配置 Task 0 网络参数  
Simulator::Schedule(Seconds(5.0), &ConfigureNetworkForTask, task0)

// t=7.0s: 开始 Task 0 传输，连接到 Server 0
Simulator::Schedule(Seconds(7.0), &StartSingleTask, client0, task0, server0)
```

## 🔧 核心实现方法

### 1. `ScheduleClientTaskSwitching`
- 为每个客户端按优先级排序任务
- 计算每个任务的执行时间线
- 使用 `Simulator::Schedule` 安排任务切换

### 2. `StartSingleTask`  
- 在正确的时间点被调用
- 动态获取当前任务的服务器地址
- 创建新的网络连接
- 立即启动传输

### 3. 网络配置切换
```cpp
// 每个任务开始前重新配置网络
ConfigureNetworkForTask(taskExecution) {
    // 根据 P_ij 设置传输功率
    phy->SetTxPowerStart(txPower)
    
    // 根据 B_ij 设置信道宽度
    phy->SetOperatingChannel(channel, frequency, width)
}
```

## 📊 验证方法

### 日志输出示例
```
=== Client 0 Task Switching Schedule ===
Client 0 Task 2 (priority=0.9):
  Compute: 0s - 3s
  Connect to Server 2 (port 9002)
  Transmit: 3s - 5s

TASK SWITCH: Client 0 starting Task 2 -> connecting to Server 2
  Server address: ********:9002
  Application started, will stop in 2 seconds

Client 0 Task 0 (priority=0.5):
  Compute: 5s - 7s  
  Connect to Server 0 (port 9000)
  Transmit: 7s - 9s

TASK SWITCH: Client 0 starting Task 0 -> connecting to Server 0
  Server address: ********:9000
  Application started, will stop in 2 seconds
```

## 🎯 关键改进

1. **真正的服务器切换**：客户端在每个任务开始时动态连接到正确的服务器
2. **动态网络配置**：每个任务执行前重新配置网络参数
3. **精确的时间控制**：使用 NS3 调度器确保任务在正确时间切换
4. **资源清理**：每个任务完成后自动停止，为下一个任务让路

## 🚀 测试

运行 `test_task_switching.py` 来验证新的任务切换实现：

```bash
# 终端1: 启动NS3仿真
cd ns3-fl-network
./ns3 run wifi_exp_multi_task_fl

# 终端2: 运行测试
python3 test_task_switching.py
```

这个新实现真正解决了您提出的问题：**客户端现在会根据执行计划中的taskId动态切换连接到不同的服务器**。
