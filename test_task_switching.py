#!/usr/bin/env python3
"""
Test script to demonstrate the new task switching mechanism in NS3 multi-task FL simulation.

This script shows how clients dynamically switch between different task servers
based on their execution plan and priority.
"""

import socket
import struct
import time

def send_test_execution_plan():
    """Send a test execution plan to demonstrate task switching."""
    
    # Connect to NS3 simulation
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.connect(('localhost', 10002))
    
    try:
        # Test scenario: 2 clients, 3 tasks
        num_clients = 2
        num_tasks = 3
        
        print("=== Task Switching Test Scenario ===")
        print(f"Clients: {num_clients}, Tasks: {num_tasks}")
        
        # Send header
        header = struct.pack('III', 1, num_clients, num_tasks)  # RUN_SIMULATION command
        sock.send(header)
        
        # Client 0: Will execute Task 2 -> Task 0 -> Task 1 (by priority)
        client_0_data = [
            # Task 0: select=1.0, T_ij=2.0s, B_ij=0.6, P_ij=0.7, Task_ij=0.5, Fs_ij=8.0MB
            1.0, 2.0, 0.6, 0.7, 0.5, 8.0,
            # Task 1: select=1.0, T_ij=1.5s, B_ij=0.8, P_ij=0.9, Task_ij=0.3, Fs_ij=6.0MB  
            1.0, 1.5, 0.8, 0.9, 0.3, 6.0,
            # Task 2: select=1.0, T_ij=3.0s, B_ij=0.4, P_ij=0.5, Task_ij=0.9, Fs_ij=10.0MB
            1.0, 3.0, 0.4, 0.5, 0.9, 10.0
        ]
        
        # Client 1: Will execute Task 1 -> Task 2 (Task 0 not selected)
        client_1_data = [
            # Task 0: select=0.2 (not selected), T_ij=1.0s, B_ij=0.5, P_ij=0.6, Task_ij=0.4, Fs_ij=5.0MB
            0.2, 1.0, 0.5, 0.6, 0.4, 5.0,
            # Task 1: select=1.0, T_ij=2.5s, B_ij=0.7, P_ij=0.8, Task_ij=0.8, Fs_ij=7.0MB
            1.0, 2.5, 0.7, 0.8, 0.8, 7.0,
            # Task 2: select=1.0, T_ij=1.8s, B_ij=0.9, P_ij=0.4, Task_ij=0.6, Fs_ij=9.0MB
            1.0, 1.8, 0.9, 0.4, 0.6, 9.0
        ]
        
        print("\n=== Expected Execution Order ===")
        print("Client 0:")
        print("  1. Task 2 (priority=0.9) -> Server 2 (port 9002)")
        print("  2. Task 0 (priority=0.5) -> Server 0 (port 9000)")  
        print("  3. Task 1 (priority=0.3) -> Server 1 (port 9001)")
        print("Client 1:")
        print("  1. Task 1 (priority=0.8) -> Server 1 (port 9001)")
        print("  2. Task 2 (priority=0.6) -> Server 2 (port 9002)")
        print("  (Task 0 not selected)")
        
        # Send client data
        for client_id in range(num_clients):
            client_header = struct.pack('I', client_id)
            sock.send(client_header)
            
            if client_id == 0:
                data = client_0_data
            else:
                data = client_1_data
                
            for value in data:
                sock.send(struct.pack('d', value))
        
        print(f"\n=== Sent execution plan to NS3 ===")
        print("Waiting for simulation results...")
        
        # Receive results
        results = []
        while True:
            try:
                # Try to receive a result
                client_id_data = sock.recv(4)
                if len(client_id_data) < 4:
                    break
                    
                client_id = struct.unpack('I', client_id_data)[0]
                task_id = struct.unpack('I', sock.recv(4))[0]
                
                # Receive task name length and name
                name_len = struct.unpack('I', sock.recv(4))[0]
                task_name = sock.recv(name_len).decode('utf-8')
                
                # Receive metrics
                bandwidth = struct.unpack('d', sock.recv(8))[0]
                power = struct.unpack('d', sock.recv(8))[0]
                packet_loss = struct.unpack('d', sock.recv(8))[0]
                throughput = struct.unpack('d', sock.recv(8))[0]
                latency = struct.unpack('d', sock.recv(8))[0]
                execution_time = struct.unpack('d', sock.recv(8))[0]
                
                results.append({
                    'client_id': client_id,
                    'task_id': task_id,
                    'task_name': task_name,
                    'bandwidth': bandwidth,
                    'power': power,
                    'packet_loss': packet_loss,
                    'throughput': throughput,
                    'latency': latency,
                    'execution_time': execution_time
                })
                
            except socket.timeout:
                break
            except Exception as e:
                print(f"Error receiving results: {e}")
                break
        
        print(f"\n=== Simulation Results ===")
        for result in results:
            print(f"Client {result['client_id']} {result['task_name']}:")
            print(f"  Server connection: Task {result['task_id']} -> Port {9000 + result['task_id']}")
            print(f"  Bandwidth: {result['bandwidth']:.1f} MHz")
            print(f"  Power: {result['power']:.1f} dBm") 
            print(f"  Throughput: {result['throughput']:.2f} Mbps")
            print(f"  Latency: {result['latency']:.2f} ms")
            print(f"  Packet Loss: {result['packet_loss']:.4f}")
            print()
            
        print("=== Task Switching Test Completed ===")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        sock.close()

if __name__ == "__main__":
    print("Starting NS3 Multi-Task FL Task Switching Test")
    print("Make sure NS3 simulation is running first!")
    print("Run: cd ns3-fl-network && ./ns3 run wifi_exp_multi_task_fl")
    print()
    
    input("Press Enter when NS3 simulation is ready...")
    send_test_execution_plan()
