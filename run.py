"""
Multi-Task Federated Learning with MADDPG and NS3 Integration

This is the main script to run the complete multi-task federated learning system
with task similarity analysis, MADDPG resource allocation, and NS3 network simulation.
"""

from datetime import datetime
import logging
import argparse
import json
import time
from pathlib import Path
from typing import Dict, Any

import torch

from multi_task_server import MultiTaskFederatedServer
from entity.entitys import MaddpgConfig, TaskConfig, TaskType, MultiTaskFLConfig


def setup_logging(log_level: str = "INFO", log_file: str = "multi_task_fl.log"):
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(), logging.FileHandler(log_file)],
    )


def create_experiment_config(
    num_clients: int = 5,
    num_rounds: int = 50,
    clients_per_round: int = 3,
    similarity_threshold: float = 0.5,
    network_type: str = "wifi",
    tasks: list = None,
    episodes: int = 100,
) -> Any:
    """Create experiment configuration"""
    # TODO Experiment Config
    # Default tasks
    if tasks is None:
        tasks = ["mnist", "cifar10", "airquality"]

    # Task configurations
    task_configs = {}
    if "mnist" in tasks:
        task_configs["mnist"] = TaskConfig(
            "mnist",
            TaskType.IMAGE_CLASSIFICATION,
            "data/MNIST",
            "cnn",
            batch_size=64,
            learning_rate=0.001,
            local_epochs=5,
            priority=1.0,
        )
    if "cifar10" in tasks:
        task_configs["cifar10"] = TaskConfig(
            "cifar10",
            TaskType.IMAGE_CLASSIFICATION,
            "data/cifar-10-batches-py",
            "resnet",
            batch_size=32,
            learning_rate=0.0001,
            local_epochs=5,
            priority=1.0,
        )
    if "airquality" in tasks:
        task_configs["airquality"] = TaskConfig(
            "airquality",
            TaskType.TIME_SERIES,
            "data/air_quality",
            "gru",
            batch_size=32,
            learning_rate=0.001,
            local_epochs=5,
            priority=0.8,
        )

    # Multi-task FL configuration (MADDPG will be initialized automatically)
    mt_config = MultiTaskFLConfig(
        num_clients=num_clients,
        num_rounds=num_rounds,
        clients_per_round=clients_per_round,
        similarity_threshold=similarity_threshold,
        tasks=task_configs,
    )

    current_time = datetime.now().strftime("%Y%m%d-%H%M%S")

    # TODO MaddpgConfig
    maddpg_config = MaddpgConfig(
        state_dim=9 * len(tasks),
        action_dim=5 * len(tasks),
        episodes=episodes,
        chkpt_dir=f"maddpg_checkpoints/tasks_{len(tasks)}/num_clients_{num_clients}/{current_time}",
    )
    # [bandwidth_ij, power_ij, task_ij, q_ij, throughout_ij, latency_ij, packetLoss_ij, execution_time_ij, accuracy, |暂时没放进去  gradient_magnitude]
    # self.state_dim = 9 * num_tasks  # = 9 for 3 tasks
    # maddpg_config.state_dim = 9 * len(tasks)
    # Action dimension: [select_ij, q_ij, B_ij, P_ij, Task_ij] for each task
    # self.action_dim = 5 * num_tasks  # = 15 for 3 tasks
    # maddpg_config.action_dim = 5 * len(tasks)

    # Main configuration object (compatible with existing FL framework)
    class ExperimentConfig:
        def __init__(self):
            self.server = "multi_task"
            self.clients = type("obj", (object,), {"total": num_clients})()
            self.paths = type(
                "obj",
                (object,),
                {
                    "model": maddpg_config.chkpt_dir,
                    "reports": f"./results/experiment_{current_time}",
                    "data": "./data",
                },
            )()
            self.model = type("obj", (object,), {"size": 2048})()

            # Network configuration
            if network_type == "wifi":
                self.network = type(
                    "obj",
                    (object,),
                    {"type": "wifi", "wifi": {"tx_gain": 0.0, "max_packet_size": 1024}},
                )()
            else:
                self.network = type(
                    "obj",
                    (object,),
                    {"type": "ethernet", "ethernet": {"max_packet_size": 1500}},
                )()

            # Data configuration
            self.data = type(
                "obj",
                (object,),
                {"IID": False, "loading": "static"},  # Non-IID for realistic FL
            )()
            self.loader = "basic"

            # Multi-task configuration
            self.multi_task = mt_config
            self.maddpg_config = maddpg_config
            device = torch.device(f"cuda:0" if torch.cuda.is_available() else "cpu")
            self.device = device
            self.maddpg_config.device = device
            print(f"Using device: {device}")
            self.current_time = current_time

    return ExperimentConfig()


def run_experiment(
    config: Any, experiment_name: str = "multi_task_fl", tensorboard: bool = False
) -> Dict[str, Any]:
    """Run the multi-task federated learning experiment"""

    logger = logging.getLogger(__name__)
    logger.info("=" * 80)
    logger.info(f"STARTING MULTI-TASK FEDERATED LEARNING EXPERIMENT: {experiment_name}")
    logger.info("=" * 80)

    # Log experiment configuration
    logger.info("Experiment Configuration:")
    logger.info(f"  Number of clients: {config.multi_task.num_clients}")
    logger.info(f"  Number of rounds: {config.multi_task.num_rounds}")
    logger.info(f"  Clients per round: {config.multi_task.clients_per_round}")
    logger.info(f"  Tasks: {list(config.multi_task.tasks.keys())}")
    logger.info(f"  Similarity threshold: {config.multi_task.similarity_threshold}")
    logger.info(f"  Network type: {config.network.type}")
    logger.info(f"  Resource allocation: MADDPG (Multi-Agent DDPG)")

    # Create results directory
    results_dir = Path(config.paths.reports)
    results_dir.mkdir(parents=True, exist_ok=True)

    # Initialize server
    logger.info("Initializing Multi-Task Federated Server...")
    server = MultiTaskFederatedServer(config, tensorboard)

    # Run experiment
    start_time = time.time()

    try:
        # TODO run_experiment
        server.run_experiment()

        experiment_duration = time.time() - start_time
        logger.info(
            f"Experiment completed successfully in {experiment_duration:.2f} seconds"
        )

        # Collect results
        results = {
            "experiment_name": experiment_name,
            "config": {
                "episodes": config.maddpg_config.episodes,
                "state_dim": config.maddpg_config.state_dim,
                "action_dim": config.maddpg_config.action_dim,
                "num_clients": config.multi_task.num_clients,
                "num_rounds": config.multi_task.num_rounds,
                "clients_per_round": config.multi_task.clients_per_round,
                "tasks": list(config.multi_task.tasks.keys()),
                "similarity_threshold": config.multi_task.similarity_threshold,
                "network_type": config.network.type,
            },
            "duration": experiment_duration,
            "epsode_metrics": server.epsode_metrics,
            "maddpg_stats": server.maddpg_allocator.get_training_stats(),
            "success": True,
        }

        # Save results
        results_file = results_dir / f"{experiment_name}_results.json"
        with open(results_file, "w") as f:
            json.dump(results, f, indent=2, default=str)

        logger.info(f"Results saved to: {results_file}")

        return results

    except Exception as e:
        logger.error(f"Experiment failed: {e}")
        import traceback

        traceback.print_exc()

        return {
            "experiment_name": experiment_name,
            "success": False,
            "error": str(e),
            "duration": time.time() - start_time,
        }


def run_ablation_study():
    """Run ablation study with different configurations"""

    logger = logging.getLogger(__name__)
    logger.info("Starting ablation study...")

    # Different configurations to test
    configurations = [
        {
            "name": "baseline_high_similarity",
            "similarity_threshold": 0.7,
            "tasks": ["mnist", "cifar10"],  # Similar image tasks
            "description": "High similarity threshold with similar tasks",
        },
        {
            "name": "baseline_low_similarity",
            "similarity_threshold": 0.3,
            "tasks": ["mnist", "cifar10"],
            "description": "Low similarity threshold with similar tasks",
        },
        {
            "name": "heterogeneous_tasks",
            "similarity_threshold": 0.5,
            "tasks": ["mnist", "cifar10", "airquality"],  # Mixed tasks
            "description": "Mixed task types with medium similarity threshold",
        },
        {
            "name": "independent_training",
            "similarity_threshold": 0.1,  # Very low threshold
            "tasks": ["mnist", "cifar10", "airquality"],
            "description": "Force independent training for all tasks",
        },
    ]

    results = {}

    for config_spec in configurations:
        logger.info(f"Running configuration: {config_spec['name']}")
        logger.info(f"Description: {config_spec['description']}")

        # Create configuration
        config = create_experiment_config(
            num_clients=5,
            num_rounds=20,  # Shorter for ablation study
            clients_per_round=3,
            similarity_threshold=config_spec["similarity_threshold"],
            tasks=config_spec["tasks"],
        )

        # Run experiment
        result = run_experiment(config, config_spec["name"])
        results[config_spec["name"]] = result

        logger.info(f"Configuration {config_spec['name']} completed")
        logger.info("-" * 60)

    # Generate ablation study report
    logger.info("=" * 80)
    logger.info("ABLATION STUDY RESULTS")
    logger.info("=" * 80)

    for name, result in results.items():
        if result["success"]:
            final_metrics = (
                result["round_metrics"][-1] if result["round_metrics"] else {}
            )
            training_metrics = final_metrics.get("training_metrics", {})
            maddpg_metrics = final_metrics.get("maddpg_metrics", {})

            logger.info(f"{name}:")
            logger.info(f"  Final Loss: {training_metrics.get('avg_loss', 'N/A'):.4f}")
            logger.info(
                f"  MADDPG Reward: {maddpg_metrics.get('avg_reward', 'N/A'):.3f}"
            )
            logger.info(f"  Duration: {result['duration']:.2f}s")
        else:
            logger.info(f"{name}: FAILED - {result.get('error', 'Unknown error')}")

    return results


def main():
    """Main function"""
    # TODO main()
    parser = argparse.ArgumentParser(
        description="Multi-Task Federated Learning with MADDPG and NS3"
    )
    parser.add_argument(
        "--mode",
        choices=["single", "ablation"],
        default="single",
        help="Run mode: single experiment or ablation study",
    )
    parser.add_argument("--num-clients", type=int, default=5, help="Number of clients")
    parser.add_argument("--episodes", type=int, default=2, help="Number of episodes")
    parser.add_argument(
        "--num-rounds", type=int, default=50, help="Number of federated learning rounds"
    )
    parser.add_argument(
        "--clients-per-round", type=int, default=3, help="Number of clients per round"
    )
    parser.add_argument(
        "--similarity-threshold",
        type=float,
        default=0.5,
        help="Task similarity threshold",
    )
    parser.add_argument(
        "--network-type",
        choices=["wifi", "ethernet"],
        default="wifi",
        help="Network type for NS3 simulation",
    )
    parser.add_argument(
        "--tasks",
        nargs="+",
        default=["mnist", "cifar10", "airquality"],
        choices=["mnist", "cifar10", "airquality"],
        help="Tasks to include in the experiment",
    )
    parser.add_argument(
        "--log-level",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Logging level",
    )
    parser.add_argument(
        "--experiment_name", default="multi_task_fl", help="Experiment name"
    )
    parser.add_argument(
        "--tensorboard",
        action="store_true",
        default=False,
        help="Set True if using TensorBoard",
    )

    args = parser.parse_args()

    # Setup logging
    setup_logging(args.log_level)

    logger = logging.getLogger(__name__)
    logger.info("Multi-Task Federated Learning System")
    logger.info("Task Correlation-Driven AI & Network Co-optimization")
    logger.info("With MADDPG Resource Allocation and NS3 Network Simulation")

    try:
        if args.mode == "single":
            # Single experiment
            config = create_experiment_config(
                num_clients=args.num_clients,
                num_rounds=args.num_rounds,
                clients_per_round=args.clients_per_round,
                similarity_threshold=args.similarity_threshold,
                network_type=args.network_type,
                tasks=args.tasks,
                episodes=args.episodes,
            )

            result = run_experiment(config, args.experiment_name, args.tensorboard)

            if result["success"]:
                logger.info("Experiment completed successfully!")
            else:
                logger.error("Experiment failed!")

        elif args.mode == "ablation":
            # Ablation study
            results = run_ablation_study()

            successful_runs = sum(1 for r in results.values() if r["success"])
            total_runs = len(results)

            logger.info(
                f"Ablation study completed: {successful_runs}/{total_runs} successful runs"
            )

    except KeyboardInterrupt:
        logger.info("Experiment interrupted by user")
    except Exception as e:
        logger.error(f"Experiment failed with error: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
